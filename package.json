{"name": "claude-flow", "version": "1.0.72", "description": "Advanced AI agent orchestration system for Claude Code", "main": "cli.js", "bin": {"claude-flow": "./cli.js"}, "scripts": {"dev": "tsx src/cli/main.ts", "build": "npm run build:ts && npm run build:binary", "build:ts": "tsc", "build:binary": "pkg dist/cli/main.js --targets node20-linux-x64,node20-macos-x64,node20-win-x64 --output bin/claude-flow", "build:simple": "npm run build:ts && pkg dist/cli/simple-cli.js --output bin/claude-flow-simple", "typecheck": "tsc --noEmit", "test": "jest", "lint": "eslint src --ext .ts,.js", "format": "prettier --write src", "prepublishOnly": "node scripts/prepare-publish.js", "prepare-publish": "node scripts/prepare-publish.js"}, "keywords": ["claude", "ai", "agent", "orchestration", "mcp", "workflow", "automation"], "author": "rUv", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ruvnet/claude-code-flow.git"}, "bugs": {"url": "https://github.com/ruvnet/claude-code-flow/issues"}, "homepage": "https://github.com/ruvnet/claude-code-flow#readme", "engines": {"node": ">=20.0.0 <25.0.0"}, "files": ["cli.js", "bin/", "dist/", "src/", ".claude/", "scripts/swarm-blessed-ui.js", "scripts/swarm-simple.js", "scripts/swarm-example.sh", "README.md", "LICENSE", "SWARM_TTY_SOLUTION.md", "SWARM_VISIBILITY.md"], "dependencies": {"@modelcontextprotocol/sdk": "^1.13.2", "@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^11.10.0", "blessed": "^0.1.81", "chalk": "^5.4.1", "cli-table3": "^0.6.5", "commander": "^11.1.0", "cors": "^2.8.5", "express": "^4.21.2", "fs-extra": "^11.3.0", "helmet": "^7.2.0", "inquirer": "^9.3.7", "nanoid": "^5.1.5", "ora": "^7.0.1", "p-queue": "^8.1.0", "ws": "^8.18.2"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.3.5", "@swc/cli": "^0.7.7", "@swc/core": "^1.12.7", "@types/blessed": "^0.1.25", "@types/cors": "^2.8.19", "@types/express": "^4.17.23", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.8", "@types/jest": "^29.5.14", "@types/node": "^20.19.1", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "pkg": "^5.8.1", "prettier": "^3.6.2", "semantic-release": "^24.2.5", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}, "pkg": {"targets": ["node20-linux-x64", "node20-macos-x64", "node20-win-x64"], "scripts": "dist/**/*.js", "outputPath": "bin", "options": ["--experimental-specifier-resolution=node"]}, "type": "module"}