#!/usr/bin/env node
/**
 * Claude-Flow CLI - Node.js Entry Point
 * Complete CLI system with all commands and flags
 */

import { CLI } from './cli-core.js';
import { setupCommands } from './commands/index.js';

// Environment setup
process.env.NODE_ENV = process.env.NODE_ENV || 'production';

// Global error handlers
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Main entry point
async function main(): Promise<void> {
  try {
    // Create CLI instance
    const cli = new CLI("claude-flow", "Advanced AI Agent Orchestration System");

    // Setup all commands (including swarm with all flags)
    setupCommands(cli);

    // Run the CLI
    await cli.run(process.argv.slice(2));

  } catch (error) {
    console.error('Failed to start Claude-Flow:', error);
    process.exit(1);
  }
}

// Run main if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}