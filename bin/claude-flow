#!/bin/sh
# <PERSON><PERSON><PERSON> Smart Dispatcher - Detects and uses the best available runtime

VERSION="1.0.72"
SCRIPT_DIR=$(dirname "$0")
ROOT_DIR=$(cd "$SCRIPT_DIR/.." && pwd)

# Quick version check
for arg in "$@"; do
  if [ "$arg" = "--version" ] || [ "$arg" = "-v" ]; then
    echo "v$VERSION"
    exit 0
  fi
done

# Use Node.js as primary runtime (Deno support phased out)
if command -v tsx >/dev/null 2>&1 && [ -f "$ROOT_DIR/src/cli/main.ts" ]; then
  # Use tsx with the complete CLI system
  exec tsx "$ROOT_DIR/src/cli/main.ts" "$@"
elif [ -f "$ROOT_DIR/src/cli/main.ts" ]; then
  # Try to use npx tsx as fallback
  exec npx tsx "$ROOT_DIR/src/cli/main.ts" "$@"
elif command -v tsx >/dev/null 2>&1 && [ -f "$ROOT_DIR/src/cli/simple-cli.ts" ]; then
  # Fallback to simple CLI if main.ts not available
  exec tsx "$ROOT_DIR/src/cli/simple-cli.ts" "$@"
elif [ -f "$ROOT_DIR/src/cli/simple-cli.ts" ]; then
  # Final fallback
  exec npx tsx "$ROOT_DIR/src/cli/simple-cli.ts" "$@"
else
  # No runtime available, show help
  echo "🧠 Claude-Flow v$VERSION - Advanced AI Agent Orchestration System"
  echo ""
  echo "⚠️  No compatible runtime found."
  echo ""
  echo "To use Claude-Flow:"
  echo "  1. Install tsx: npm install -g tsx"
  echo "  2. Run: claude-flow <command>"
  echo ""
  echo "Or use npx directly:"
  echo "  npx tsx src/cli/simple-cli.ts <command>"
  echo ""
  echo "Documentation: https://github.com/ruvnet/claude-code-flow"
  exit 1
fi