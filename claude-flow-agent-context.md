# Claude-Flow Agent Context: System State & Technical Specifications

## System Status Overview

### Current Architecture State
- **Total Codebase**: 28,700+ lines of enterprise-grade code
- **CLI System**: ✅ UNIFIED - Node.js (tsx) runtime, Deno phase-out complete
- **Build System**: ✅ UNIFIED - Node.js (tsx) pipeline consolidated
- **Core Infrastructure**: ✅ VERIFIED - 22/22 major components operational
- **Enterprise Features**: ✅ VERIFIED - 18/18 components implemented

### Critical Issues Requiring Immediate Attention
| Issue | Priority | Files Affected |
|-------|----------|----------------|
| TypeScript Errors | CRITICAL | `src/coordination/swarm-coordinator.ts` |
| Test System Performance | HIGH | Jest configuration, mock implementations |
| WebSocket Stability | HIGH | `src/ui/console/js/websocket-client.js` |

### Core Architecture Issues (Phase 3)
**Reference**: See `/workspaces/claude-code-flow/Issues.md` for complete issue descriptions and analysis

| Issue | Root Cause | Impact | Key Files |
|-------|------------|--------|-----------|
| Fragile State Management with JSON Files | JSON file race conditions in concurrent operations | Data integrity risks, corruption | `memory/memory-store.json`, `memory/claude-flow-data.json` |
| In-Process Synchronous Task Execution | Long-running tasks blocking main orchestrator process | Scalability bottleneck, unresponsive system | `src/swarm/direct-executor.ts`, `src/swarm/sparc-executor.ts` |
| Architectural Circular Dependencies | High-level components creating dependency cycles | Rigid system, testing difficulties | `src/core/orchestrator.ts`, `src/agents/agent-manager.ts` |
| Direct Spawning of External Processes | Unabstracted shell command execution | Security risks, environment coupling | `bin/` scripts, `src/swarm/coordinator.ts` |
| Brittle Agent Spawning Mechanism | Platform-dependent shell scripts for agent lifecycle | Limited deployment environments | `bin/claude-flow-swarm`, `scripts/spawn-claude-terminal.sh` |

## Technical Infrastructure Analysis

### Enterprise Components (Verified & Operational)

#### Swarm Coordination System
- **Location**: `src/coordination/`
- **Key Files**: 
  - `swarm-coordinator.ts` (821 lines) - Main coordination logic
  - `work-stealing.ts` (224 lines) - Load balancing algorithms
  - `circuit-breaker.ts` (366 lines) - Fault tolerance patterns
  - `advanced-scheduler.ts` (487 lines) - Task scheduling with dependencies
- **Capabilities**: Work stealing, circuit breakers, advanced scheduling, agent workload management
- **Status**: ✅ OPERATIONAL with 4 TypeScript errors requiring fixes

#### Memory Management System
- **Location**: `src/memory/`
- **Key Files**:
  - `distributed-memory.ts` (995 lines) - Distributed memory coordination
  - `advanced-memory-manager.ts` (1,957 lines) - Core memory management
  - `backends/sqlite.ts` (326 lines) - SQLite persistence
  - `backends/markdown.ts` - Markdown backend
  - `cache.ts` (238 lines) - Caching layer
- **Features**: Distributed memory, encryption, compression, multiple backends, cross-agent coordination
- **Status**: ✅ OPERATIONAL

#### MCP Server Implementation
- **Location**: `src/mcp/`
- **Key Files**:
  - `server.ts` (608 lines) - Main MCP server
  - `session-manager.ts` (418 lines) - Session management
  - `load-balancer.ts` (510 lines) - Load balancing
  - `transports/` - Multiple transport implementations
- **Features**: Session management, authentication, load balancing, multiple transports
- **Status**: ✅ OPERATIONAL

#### CLI System (Post-Migration)
- **Primary Entry**: `bin/claude-flow` (Node.js tsx priority)
- **Main Implementation**: `src/cli/main.ts` (rewritten for Node.js)
- **Core System**: `src/cli/commands/index.ts` - Complete command definitions
- **Swarm Commands**: `src/cli/commands/swarm.ts` - All flags operational
- **Status**: ✅ UNIFIED - All documented flags working

### SPARC Mode Ecosystem
- **Location**: `src/cli/simple-commands/sparc-modes/`
- **Configuration**: `.roomodes`
- **Documentation**: `.claude/commands/sparc/sparc-modes.md`
- **Available Modes**: 26+ specialized modes including architect, coder, tdd, reviewer, debugger, researcher, orchestrator
- **Status**: ✅ OPERATIONAL

## Critical Technical Issues

### 1. TypeScript Compilation Errors (CRITICAL)
**Location**: `src/coordination/swarm-coordinator.ts`
**Specific Issues**:
```typescript
// Error 1: Missing stop() method in OptimizedExecutor
// Line 235: Property 'stop' does not exist on type 'OptimizedExecutor'
// Fix: OptimizedExecutor has shutdown() method, not stop()

// Error 2: TaskPriority type mismatch  
// Line 470: Type '"medium"' is not assignable to type 'TaskPriority'
// Fix: TaskPriority enum expects 'normal' not 'medium'

// Error 3: TaskRequirements structure mismatch
// Line 479: Type 'never[]' is missing properties: capabilities, tools, permissions
// Fix: Empty array instead of proper TaskRequirements object

// Error 4: TaskConstraints property mismatch
// Line 483: 'timeout' does not exist in type 'TaskConstraints'
// Fix: Property should be 'timeoutAfter' not 'timeout'
```

### 2. Test System Performance Issues (HIGH)
**Root Causes**:
- Jest Haste module naming collisions from examples directory
- Mock implementation failures: `logger.info is not a function`
- Test timeout issues and incorrect expect() usage patterns
- Missing dependencies: `p-queue` module

**Key Files**:
- Jest configuration needs examples directory exclusion
- Mock implementations in test files
- `src/swarm/optimizations/async-file-manager.ts` - missing p-queue dependency

### 3. WebSocket Stability Issues (HIGH)
**Location**: `src/ui/console/js/websocket-client.js`
**Problem**: WebSocket connects but disconnects immediately on port 3000
**Root Causes**:
- Heartbeat mechanism causing premature disconnections
- MCP server session management instability
- Connection retry logic insufficient for production use

## Core Architecture Problems (Phase 3)
**Reference**: `/workspaces/claude-code-flow/Issues.md` contains detailed analysis and recommendations for these issues

### Fragile State Management with JSON Files
**Problem Description**: Multi-agent concurrent system writing to single JSON files leads to race conditions, data loss, and corruption. Performance degrades as files grow.

**Problem Files**:
- `memory/memory-store.json` - Primary state storage causing race conditions
- `memory/claude-flow-data.json` - Secondary state file
- Multiple uncoordinated persistence mechanisms between JSON files and `src/memory/` module

**Existing Infrastructure to Leverage**:
- `src/memory/advanced-memory-manager.ts` (1,957 lines) - Core memory management
- `src/memory/backends/sqlite.ts` (326 lines) - SQLite backend implementation
- `src/persistence/sqlite/` - Complete database layer with migrations

### In-Process Synchronous Task Execution
**Problem Description**: Long-running agent tasks executed directly within main orchestrator process block the event loop, making orchestrator unresponsive and creating critical scalability bottleneck.

**Problem Files**:
- `src/swarm/direct-executor.ts` - Blocks main process
- `src/swarm/sparc-executor.ts` - Synchronous execution
- `src/cli/simple-commands/swarm-executor.js` - Synchronous file operations

**Existing Infrastructure to Leverage**:
- `src/coordination/advanced-scheduler.ts` (487 lines) - Task scheduling with dependencies
- `src/task/engine.ts` (692 lines) - Workflow management system
- `src/communication/message-bus.ts` (1,433 lines) - Enterprise messaging infrastructure

### Architectural Circular Dependencies
**Problem Description**: High-level manager and coordinator components create circular dependencies making system rigid, hard to initialize, and impossible to unit test in isolation.

**Problem Areas**:
```typescript
// Circular dependency examples:
// Orchestrator → AgentManager → Orchestrator (resource requests)
// Scheduler → Manager → Scheduler (coordination cycles)
```

**Problem Files**:
- `src/core/orchestrator.ts` (1,234 lines) - Circular dependencies with AgentManager
- `src/agents/agent-manager.ts` (1,325 lines) - Circular dependencies with Orchestrator
- `src/coordination/scheduler.ts` - Coordination cycles
- `src/coordination/manager.ts` - Management cycles

### Direct External Process Spawning
**Problem Description**: Application frequently spawns child processes directly, creating security risks (command injection) and tight coupling to system environment.

**Problem Files**:
- `bin/` scripts - Direct shell command execution
- `src/swarm/coordinator.ts` - Direct process spawning
- `src/cli/simple-commands/sparc.js` - Process execution

### Brittle Agent Spawning Mechanism
**Problem Description**: Agent spawning relies on platform-dependent shell scripts using `osascript` or `gnome-terminal`, creating tight coupling to desktop environments and preventing headless operation.

**Problem Files**:
- `bin/claude-flow-swarm` - Platform-dependent spawning
- `scripts/spawn-claude-terminal.sh` - Shell script dependencies
- `src/swarm/coordinator.ts` - Agent lifecycle management

**Existing Infrastructure to Leverage**:
- `src/mcp/server.ts` (608 lines) - MCP server for API-based communication
- `src/agents/agent-manager.ts` (1,325 lines) - Agent lifecycle management
- `src/communication/message-bus.ts` (1,433 lines) - Message-based coordination

## Agent Guidance & Starting Points

### For State Management Agents
- Focus on `src/memory/` directory for existing infrastructure
- Leverage `advanced-memory-manager.ts` as foundation
- Examine `backends/sqlite.ts` for database patterns
- Target elimination of direct JSON file access in `memory/` directory

### For Async Execution Agents
- Analyze `src/swarm/` directory for current execution patterns
- Review `src/coordination/advanced-scheduler.ts` for task management
- Focus on `src/swarm/direct-executor.ts` and `src/swarm/sparc-executor.ts` for transformation
- Leverage existing circuit breaker and work stealing patterns in `src/coordination/`

### For Module Boundary Agents
- Map dependencies starting from `src/core/orchestrator.ts`
- Analyze `src/agents/agent-manager.ts` for circular dependency patterns
- Review `src/coordination/` for existing abstraction patterns
- Focus on `bin/` scripts for process spawning abstraction

### For Integration Agents
- Coordinate with existing `src/coordination/swarm-coordinator.ts` infrastructure
- Leverage `src/mcp/` server capabilities for agent communication
- Use `src/memory/distributed-memory.ts` for cross-agent coordination
- Monitor through existing `src/terminal/manager.ts` capabilities

## Success Criteria for Agents

### Phase 1 (Critical Fixes)
- Zero TypeScript compilation errors
- Test system performance optimized
- WebSocket connections stable

### Phase 3 (Core Architecture)
- Centralized state management implemented
- Asynchronous operation coverage maximized
- Circular dependencies eliminated
- Module boundaries clearly defined with proper abstraction
- Performance improvements verified
- Data integrity guaranteed

## Technical Context from Analysis

### Build System (Resolved)
- **Status**: ✅ UNIFIED Node.js (tsx) architecture
- **Primary Runtime**: Node.js with tsx
- **Binary Generation**: pkg tool for cross-platform binaries
- **Development Workflow**: tsx for development, tsc for production builds

### Enterprise Infrastructure Available
- **Resource Management**: `src/resources/resource-manager.ts` (1,866 lines) - QoS, auto-scaling
- **Agent Management**: `src/agents/agent-manager.ts` (1,325 lines) - Lifecycle management
- **Task Engine**: `src/task/engine.ts` (692 lines) - Workflow management
- **Message Bus**: `src/communication/message-bus.ts` (1,433 lines) - Enterprise messaging
- **Terminal Management**: `src/terminal/manager.ts` (308 lines) - VSCode integration

## Key Technical Interfaces & Patterns

### State Management Interfaces
```typescript
interface DistributedMemoryConfig {
  namespace: string;
  distributed: boolean;
  consistency: ConsistencyLevel;
  replicationFactor: number;
  syncInterval: number;
  maxMemorySize: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
  backupEnabled: boolean;
  persistenceEnabled: boolean;
  shardingEnabled: boolean;
  cacheSize: number;
  cacheTtl: number;
}

interface MemoryIndex {
  keys: Map<string, string[]>;
  tags: Map<string, string[]>;
  types: Map<string, string[]>;
  namespaces: Map<string, string[]>;
  owners: Map<string, string[]>;
  fullText: Map<string, string[]>;
}
```

### Swarm Coordination Interfaces
```typescript
interface SwarmAgent {
  id: string;
  type: 'researcher' | 'developer' | 'analyzer' | 'coordinator' | 'reviewer';
  status: 'idle' | 'busy' | 'failed' | 'completed';
  capabilities: string[];
  metrics: {
    tasksCompleted: number;
    tasksFailed: number;
    totalDuration: number;
    lastActivity: Date;
  };
}

interface WorkStealingConfig {
  stealThreshold: number;
  maxStealBatch: number;
  stealInterval: number;
}

interface CircuitBreakerMetrics {
  state: CircuitState;
  failures: number;
  successes: number;
  totalRequests: number;
  rejectedRequests: number;
  halfOpenRequests: number;
}
```

### Task Engine Interfaces
```typescript
interface WorkflowTask extends Omit<Task, 'dependencies'> {
  dependencies: TaskDependency[];
  resourceRequirements: ResourceRequirement[];
  schedule?: TaskSchedule;
  retryPolicy?: {
    maxAttempts: number;
    backoffMs: number;
    backoffMultiplier: number;
  };
  checkpoints: TaskCheckpoint[];
  rollbackStrategy: 'previous-checkpoint' | 'initial-state' | 'custom';
}
```

### Resource Management Interfaces
```typescript
interface ResourceRequirements {
  cpu?: ResourceSpec;
  memory?: ResourceSpec;
  disk?: ResourceSpec;
  network?: ResourceSpec;
  gpu?: ResourceSpec;
  custom?: Record<string, ResourceSpec>;
  constraints?: ResourceConstraints;
  preferences?: ResourcePreferences;
}

interface QoSConfig {
  guarantees: QoSGuarantee[];
  objectives: QoSObjective[];
  violations: QoSViolationPolicy;
}
```

## Database & Persistence Context

### SQLite Configuration (Production)
```typescript
// Performance optimization pragmas in use:
this.db.pragma('journal_mode = WAL');
this.db.pragma('synchronous = NORMAL');
this.db.pragma('cache_size = -64000'); // 64MB
this.db.pragma('mmap_size = 268435456'); // 256MB

interface ConnectionPoolOptions {
  min: number;
  max: number;
  acquireTimeout: number;
  idleTimeout: number;
  filename: string;
  readonly?: boolean;
}
```

### Data Models Available
- **Location**: `src/persistence/sqlite/models/`
- **Models**: agents, audit, config, knowledge, memory, messages, objectives, projects, tasks
- **Migration System**: `src/persistence/sqlite/migrations/migration-runner.ts` (325 lines)

## MCP Server Technical Details

### Server Architecture
```typescript
interface IMCPServer {
  start(): Promise<void>;
  stop(): Promise<void>;
  registerTool(tool: MCPTool): void;
  getHealthStatus(): Promise<{
    healthy: boolean;
    error?: string;
    metrics?: Record<string, number>;
  }>;
  getMetrics(): MCPMetrics;
  getSessions(): MCPSession[];
  terminateSession(sessionId: string): void;
}
```

### Message Bus Configuration
```typescript
interface MessageBusConfig {
  strategy: CommunicationStrategy;
  enablePersistence: boolean;
  enableReliability: boolean;
  enableOrdering: boolean;
  maxMessageSize: number;
  maxQueueSize: number;
  acknowledgmentTimeout: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}
```

## Agent Coordination Patterns

### Memory Coordination for Swarms
- **Namespace Pattern**: Use `phase3-{domain}` for coordination (e.g., `phase3-state-management`)
- **Cross-Agent Communication**: Leverage existing `src/communication/message-bus.ts`
- **Progress Tracking**: Use `src/coordination/advanced-scheduler.ts` for task dependencies
- **Resource Allocation**: Coordinate through `src/resources/resource-manager.ts`

### Integration Points
- **State Coordination**: `src/memory/distributed-memory.ts` for shared context
- **Task Management**: `src/task/engine.ts` for workflow coordination
- **Agent Lifecycle**: `src/agents/agent-manager.ts` for agent management
- **Terminal Integration**: `src/terminal/manager.ts` for VSCode integration

## Critical Dependencies & Requirements

### Node.js Dependencies
- **p-queue**: Required by swarm optimization system (currently missing)
- **tsx**: Primary runtime for CLI execution
- **sqlite3**: Database backend for persistence
- **crypto**: Updated to modern APIs (createCipheriv, createDecipheriv)

### File System Structure
```
src/
├── coordination/     # Swarm coordination infrastructure
├── memory/          # Distributed memory management
├── mcp/             # MCP server implementation
├── cli/             # Unified CLI system (Node.js)
├── agents/          # Agent management
├── task/            # Task engine
├── communication/   # Message bus
├── resources/       # Resource management
├── terminal/        # Terminal management
└── persistence/     # Database layer
```

This context provides agents with the essential technical information needed to understand the system state and execute improvements effectively.
