#!/usr/bin/env -S deno run --allow-all
/**
 * Verification script for swarm command flag fixes
 * Tests the three fixed flags (--review, --research, --memory-namespace) without running actual swarms
 */

import { colors } from "https://deno.land/x/cliffy@v1.0.0-rc.3/ansi/colors.ts";

interface TestResult {
  name: string;
  passed: boolean;
  output?: string;
  error?: string;
}

async function runCommand(command: string[]): Promise<{ stdout: string; stderr: string; success: boolean }> {
  try {
    const process = new Deno.Command(command[0], {
      args: command.slice(1),
      stdout: "piped",
      stderr: "piped",
    });
    
    const { code, stdout, stderr } = await process.output();
    
    return {
      stdout: new TextDecoder().decode(stdout),
      stderr: new TextDecoder().decode(stderr),
      success: code === 0
    };
  } catch (error) {
    return {
      stdout: "",
      stderr: error.message,
      success: false
    };
  }
}

async function testFlag(flagName: string, flagValue?: string): Promise<TestResult> {
  const command = [
    "deno", "run", "--allow-all", 
    "./src/cli/main.ts", 
    "swarm", 
    "Test objective for verification",
    "--dry-run"
  ];
  
  if (flagValue) {
    command.push(flagName, flagValue);
  } else {
    command.push(flagName);
  }
  
  const result = await runCommand(command);
  
  return {
    name: `Flag ${flagName}${flagValue ? ` with value "${flagValue}"` : ''}`,
    passed: result.success && result.stdout.includes("DRY RUN"),
    output: result.stdout,
    error: result.stderr
  };
}

async function testHelpOutput(): Promise<TestResult> {
  const command = [
    "deno", "run", "--allow-all", 
    "./src/cli/main.ts", 
    "swarm", 
    "--help"
  ];
  
  const result = await runCommand(command);
  
  const hasReview = result.stdout.includes("--review");
  const hasResearch = result.stdout.includes("--research");
  const hasMemoryNamespace = result.stdout.includes("--memory-namespace");
  
  return {
    name: "Help text includes all three flags",
    passed: result.success && hasReview && hasResearch && hasMemoryNamespace,
    output: result.stdout,
    error: result.stderr
  };
}

async function testDryRunOutput(): Promise<TestResult> {
  const command = [
    "deno", "run", "--allow-all", 
    "./src/cli/main.ts", 
    "swarm", 
    "Test comprehensive flag verification",
    "--review",
    "--research", 
    "--memory-namespace", "test-verification",
    "--strategy", "research",
    "--max-agents", "6",
    "--parallel",
    "--monitor",
    "--coordinator",
    "--dry-run"
  ];
  
  const result = await runCommand(command);
  
  // Check for expected patterns in dry run output
  const hasReviewEnabled = result.stdout.includes("Review:") || result.stdout.includes("review");
  const hasResearchEnabled = result.stdout.includes("Research:") || result.stdout.includes("research");
  const hasMemoryNamespace = result.stdout.includes("test-verification") || result.stdout.includes("Memory");
  
  return {
    name: "Dry run shows all flags in configuration",
    passed: result.success && hasReviewEnabled && hasResearchEnabled && hasMemoryNamespace,
    output: result.stdout,
    error: result.stderr
  };
}

async function testFlagCombinations(): Promise<TestResult[]> {
  const combinations = [
    {
      name: "Review flag only",
      flags: ["--review"]
    },
    {
      name: "Research flag only", 
      flags: ["--research"]
    },
    {
      name: "Memory namespace flag only",
      flags: ["--memory-namespace", "custom-test"]
    },
    {
      name: "Review + Research flags",
      flags: ["--review", "--research"]
    },
    {
      name: "All three flags together",
      flags: ["--review", "--research", "--memory-namespace", "comprehensive-test"]
    },
    {
      name: "All flags with existing flags",
      flags: ["--review", "--research", "--memory-namespace", "full-test", "--parallel", "--monitor", "--coordinator"]
    }
  ];
  
  const results: TestResult[] = [];
  
  for (const combo of combinations) {
    const command = [
      "deno", "run", "--allow-all", 
      "./src/cli/main.ts", 
      "swarm", 
      `Test ${combo.name}`,
      "--dry-run",
      ...combo.flags
    ];
    
    const result = await runCommand(command);
    
    results.push({
      name: combo.name,
      passed: result.success && result.stdout.includes("DRY RUN"),
      output: result.stdout,
      error: result.stderr
    });
  }
  
  return results;
}

async function main() {
  console.log(colors.cyan.bold("🔍 Swarm Flag Verification Script"));
  console.log(colors.yellow("Testing --review, --research, and --memory-namespace flags\n"));
  
  const allTests: TestResult[] = [];
  
  // Test 1: Help output
  console.log(colors.blue("📚 Testing help output..."));
  const helpTest = await testHelpOutput();
  allTests.push(helpTest);
  
  // Test 2: Individual flags
  console.log(colors.blue("🏷️  Testing individual flags..."));
  const reviewTest = await testFlag("--review");
  const researchTest = await testFlag("--research");
  const memoryTest = await testFlag("--memory-namespace", "test-namespace");
  allTests.push(reviewTest, researchTest, memoryTest);
  
  // Test 3: Comprehensive dry run
  console.log(colors.blue("🧪 Testing comprehensive dry run..."));
  const dryRunTest = await testDryRunOutput();
  allTests.push(dryRunTest);
  
  // Test 4: Flag combinations
  console.log(colors.blue("🔗 Testing flag combinations..."));
  const combinationTests = await testFlagCombinations();
  allTests.push(...combinationTests);
  
  // Report results
  console.log(colors.cyan.bold("\n📊 Test Results:"));
  console.log("=".repeat(60));
  
  let passed = 0;
  let failed = 0;
  
  for (const test of allTests) {
    if (test.passed) {
      console.log(colors.green(`✅ ${test.name}`));
      passed++;
    } else {
      console.log(colors.red(`❌ ${test.name}`));
      if (test.error) {
        console.log(colors.red(`   Error: ${test.error}`));
      }
      failed++;
    }
  }
  
  console.log("=".repeat(60));
  console.log(colors.cyan(`Total: ${allTests.length} tests`));
  console.log(colors.green(`Passed: ${passed}`));
  console.log(colors.red(`Failed: ${failed}`));
  
  if (failed === 0) {
    console.log(colors.green.bold("\n🎉 All tests passed! Flag fixes are working correctly."));
    console.log(colors.yellow("\n✨ Summary of fixes:"));
    console.log("   • --review flag: Enable peer review between agents");
    console.log("   • --research flag: Enable research capabilities for all agents");
    console.log("   • --memory-namespace flag: Shared memory namespace (default: swarm)");
    console.log("\n📝 These flags are now properly connected to the swarm execution logic.");
  } else {
    console.log(colors.red.bold("\n❌ Some tests failed. Please check the implementation."));
    Deno.exit(1);
  }
}

if (import.meta.main) {
  await main();
}
